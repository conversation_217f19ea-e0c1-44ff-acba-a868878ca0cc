import React, { useState, useEffect } from "react";
import toast from "react-hot-toast";
import { useNavigate, useParams } from "react-router-dom";
import {
  Play,
  CheckCircle,
  Star,
  TrendingUp,
  MessageSquare,
  Trophy,
  AlertCircle,
  Trash2,
  Send,
  Video,
  Award,
  Target,
} from "lucide-react";
import ReportErrorDisplay from "./ReportErrorDisplay";

function ReviewVideoProfile({ videoUrl = "", finishVideoProfile = () => {} }) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [score, setScore] = useState({
    score: {
      Communication_Score: 0,
      Skill_Score: 0,
      Overall_Score: 0,
    },
    evaluation: [], // New field for detailed question-wise report
    Suggestions: "",
    Next_Focus_Areas: [], // New field for next focus areas
    error: null, // Add error field to score state
  });
  const [status, setStatus] = useState("NotSubmitted");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { vpid } = useParams();
  const [toolTip, showToolTip] = useState(false);
  const navigate = useNavigate();

  // Initialize from localStorage on mount
  useEffect(() => {
    try {
      setIsLoading(true);
      const storedData = JSON.parse(
        localStorage.getItem(`ReviewVideoProfile${vpid}`)
      );
      if (storedData) {
        // Ensure score and videoUrl are correctly set from storedData
        setScore(storedData.score);
        setStatus(storedData.status);
      }
    } catch (err) {
      console.error("Error loading review data:", err);
      setError("Failed to load review data");
    } finally {
      setIsLoading(false);
    }
  }, [vpid]);
  const handleDelete = async () => {
    try {
      const videoProfileId = vpid;
      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/video-resume/${videoProfileId}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        toast.success("Video profile discarded successfully");
        navigate("/candidate/dashboard");
        localStorage.removeItem(`questions`);
        localStorage.removeItem(`ReviewVideoProfile${vpid}`);
        window.location.reload(); // Ensure the page is reloaded to reflect changes
      } else {
        const { message } = await response.json();
        toast.error(message);
        console.error("Failed to delete video profile");
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };
  if (score.error) {
    return <ReportErrorDisplay />;
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 dark:bg-gray-900">
      <div className="mx-auto max-w-7xl">
        {/* Header */}
        <div className="mb-8 text-center">
          <div className="to-emerald-100 dark:to-emerald-900/50 mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-green-100 dark:from-green-900/50">
            <Trophy className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
          <h1 className="mb-2 text-3xl font-bold text-gray-900 dark:text-white">
            Review Your Video Resume
          </h1>
          <p className="mx-auto max-w-2xl text-gray-600 dark:text-gray-400">
            Your AI-powered interview has been completed and analyzed. Review
            your performance and submit when ready.
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
          {/* Left Side: Video Preview */}
          <div className="space-y-6">
            {/* Video Player */}
            <div className="overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900">
              <div className="border-b border-gray-200 p-4 dark:border-gray-800">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-gradient-to-br from-red-100 to-pink-100 p-2 dark:from-red-900/50 dark:to-pink-900/50">
                    <Video className="h-5 w-5 text-red-600 dark:text-red-400" />
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                      Interview Recording
                    </h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Your complete video resume session
                    </p>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <div
                  className="relative overflow-hidden rounded-lg bg-gray-900 shadow-lg"
                  style={{ aspectRatio: "16/9" }}
                >
                  <video
                    src={videoUrl}
                    className="h-full w-full object-cover"
                    controls
                    controlsList="nodownload"
                  />
                  {!videoUrl && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-800">
                      <div className="text-center text-white">
                        <Play className="mx-auto mb-2 h-12 w-12 opacity-50" />
                        <p className="text-sm opacity-75">
                          Video not available
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Overview Section */}
            <div className="rounded-xl border border-blue-200 bg-gradient-to-br from-blue-50 to-purple-50 dark:border-blue-800 dark:from-blue-900/20 dark:to-purple-900/20">
              <div className="border-b border-blue-200 p-4 dark:border-blue-800">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-blue-100 p-2 dark:bg-blue-900/50">
                    <AlertCircle className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                      About This Analysis
                    </h3>
                  </div>
                </div>
              </div>
              <div className="p-4">
                <div className="space-y-4">
                  <p className="text-sm leading-relaxed text-blue-800 dark:text-blue-200">
                    Your interview has been evaluated using advanced AI
                    technology that assesses communication skills, technical
                    knowledge, and overall presentation. The scores below
                    reflect your performance across key areas that employers
                    value most.
                  </p>

                  {!isLoading && !error && !score.error ? (
                    <>
                      <div>
                        <h4 className="mb-2 text-sm font-semibold text-blue-900 dark:text-blue-100">
                          Suggestions
                        </h4>
                        <p className="text-sm leading-relaxed text-blue-800 dark:text-blue-200">
                          {score.Suggestions}
                        </p>
                      </div>
                      {score.evaluation && score.evaluation.length > 0 && (
                        <details className="mt-6">
                          <summary className="cursor-pointer text-sm font-semibold text-blue-900 dark:text-blue-100 bg-blue-100 dark:bg-blue-900/20 rounded px-2 py-1">
                            Click here for Detailed Question-wise Evaluation
                          </summary>
                          <div className="space-y-4 mt-4">
                            {score.evaluation.map((item, index) => (
                              <div
                                key={index}
                                className="rounded-md border border-blue-200 bg-white/50 p-4 dark:border-blue-800 dark:bg-blue-900/20"
                              >
                                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                                  <span className="font-bold">Question:</span>{" "}
                                  {item.Question}
                                </p>
                                <p className="mt-1 text-sm text-blue-800 dark:text-blue-200">
                                  <span className="font-bold">Your Answer:</span>{" "}
                                  {/^(import|const|let|function|class|interface|type|export|async|await|\s*<\w)/.test(item.Your_Answer?.trim()) ? (
                                    <pre className="bg-gray-100 dark:bg-gray-800 rounded p-2 overflow-x-auto">
                                      <code className="language-jsx">
                                        {item.Your_Answer}
                                      </code>
                                    </pre>
                                  ) : (
                                    item.Your_Answer || "Not provided"
                                  )}
                                </p>
                                <p className="mt-1 text-sm text-blue-800 dark:text-blue-200">
                                  <span className="font-bold">
                                    Expected Answer:
                                  </span>{" "}
                                  {item.Expected_Answer}
                                </p>
                                <div className="mt-2 border-t border-blue-200 pt-2 dark:border-blue-800">
                                  <p className="text-sm text-blue-800 dark:text-blue-200">
                                    <span className="font-bold">
                                      Technical Understanding:
                                    </span>{" "}
                                    {item.Analysis?.Technical_Understanding}
                                  </p>
                                  <p className="text-sm text-blue-800 dark:text-blue-200">
                                    <span className="font-bold">
                                      Communication Quality:
                                    </span>{" "}
                                    {item.Analysis?.Communication_Quality}
                                  </p>
                                  {item.Analysis?.Improvement_Areas &&
                                    item.Analysis.Improvement_Areas.length >
                                      0 && (
                                    <p className="text-sm text-blue-800 dark:text-blue-200">
                                      <span className="font-bold">
                                        Improvement Areas:
                                      </span>{" "}
                                      {item.Analysis.Improvement_Areas.join(
                                        ", "
                                      )}
                                    </p>
                                  )}
                                  {item.Analysis?.Strengths &&
                                    item.Analysis.Strengths.length > 0 && (
                                    <p className="text-sm text-blue-800 dark:text-blue-200">
                                      <span className="font-bold">
                                        Strengths:
                                      </span>{" "}
                                      {item.Analysis.Strengths.join(", ")}
                                    </p>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </details>
                      )}

                    </>
                  ) : score.error ? (
                    <div className="mb-4 rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
                      <div className="flex items-start gap-3">
                        <AlertCircle className="mt-0.5 h-5 w-5 flex-shrink-0 text-red-600 dark:text-red-400" />
                        <div>
                          <h4 className="mb-1 font-medium text-red-900 dark:text-red-100">
                            Report Generation Failed
                          </h4>
                          <p className="text-sm text-red-800 dark:text-red-200">
                            There was a technical issue generating your report. Please contact support.
                          </p>
                        </div>
                      </div>
                    </div>
                  ) : null}
                </div>
              </div>
            </div>
          </div>

          {/* Right Side: Score and Analysis */}
          <div className="space-y-6">
            {/* Performance Overview */}
            <div className="rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900">
              <div className="border-b border-gray-200 p-4 dark:border-gray-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="to-emerald-100 dark:to-emerald-900/50 rounded-lg bg-gradient-to-br from-green-100 p-2 dark:from-green-900/50">
                      <Award className="h-5 w-5 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Performance Scores
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        AI-powered evaluation results
                      </p>
                    </div>
                  </div>
                  {score.error && (
                    <span className="inline-flex items-center rounded-full bg-red-100 px-3 py-1 text-xs font-medium text-red-800 dark:bg-red-900/50 dark:text-red-200">
                      <AlertCircle className="mr-1 h-3 w-3" />
                      Error in Report Generation
                    </span>
                  )}
                </div>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  {/* Skill Score */}
                  <div className="text-center">
                    <div
                      className={`relative mx-auto mb-4 flex h-24 w-24 items-center justify-center rounded-full ${
                        score.score.Skill_Score < 5
                          ? "bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/50 dark:to-red-800/50"
                          : score.score.Skill_Score <= 7
                          ? "bg-gradient-to-br from-amber-100 to-orange-200 dark:from-amber-900/50 dark:to-orange-800/50"
                          : "to-emerald-200 dark:to-emerald-800/50 bg-gradient-to-br from-green-100 dark:from-green-900/50"
                      }`}
                    >
                      <div className="text-center">
                        <Target
                          className={`mx-auto mb-1 h-6 w-6 ${
                            score.score.Skill_Score < 5
                              ? "text-red-600 dark:text-red-400"
                              : score.score.Skill_Score <= 7
                              ? "text-amber-600 dark:text-amber-400"
                              : "text-green-600 dark:text-green-400"
                          }`}
                        />
                        <div
                          className={`text-2xl font-bold ${
                            score.score.Skill_Score < 5
                              ? "text-red-700 dark:text-red-300"
                              : score.score.Skill_Score <= 7
                              ? "text-amber-700 dark:text-amber-300"
                              : "text-green-700 dark:text-green-300"
                          }`}
                        >
                          {score.score.Skill_Score || 0}
                        </div>
                      </div>
                    </div>
                    <h4 className="mb-1 font-semibold text-gray-900 dark:text-white">
                      Technical Skills
                    </h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Knowledge & Expertise
                    </p>
                  </div>

                  {/* Communication Score */}
                  <div className="text-center">
                    <div
                      className={`relative mx-auto mb-4 flex h-24 w-24 items-center justify-center rounded-full ${
                        score.score.Communication_Score < 5
                          ? "bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/50 dark:to-red-800/50"
                          : score.score.Communication_Score <= 7
                          ? "bg-gradient-to-br from-amber-100 to-orange-200 dark:from-amber-900/50 dark:to-orange-800/50"
                          : "to-emerald-200 dark:to-emerald-800/50 bg-gradient-to-br from-green-100 dark:from-green-900/50"
                      }`}
                    >
                      <div className="text-center">
                        <MessageSquare
                          className={`mx-auto mb-1 h-6 w-6 ${
                            score.score.Communication_Score < 5
                              ? "text-red-600 dark:text-red-400"
                              : score.score.Communication_Score <= 7
                              ? "text-amber-600 dark:text-amber-400"
                              : "text-green-600 dark:text-green-400"
                          }`}
                        />
                        <div
                          className={`text-2xl font-bold ${
                            score.score.Communication_Score < 5
                              ? "text-red-700 dark:text-red-300"
                              : score.score.Communication_Score <= 7
                              ? "text-amber-700 dark:text-amber-300"
                              : "text-green-700 dark:text-green-300"
                          }`}
                        >
                          {score.score.Communication_Score || 0}
                        </div>
                      </div>
                    </div>
                    <h4 className="mb-1 font-semibold text-gray-900 dark:text-white">
                      Communication
                    </h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Clarity & Expression
                    </p>
                  </div>

                  {/* Overall Score */}
                  <div className="text-center">
                    <div
                      className={`relative mx-auto mb-4 flex h-24 w-24 items-center justify-center rounded-full ${
                        score.score.Overall_Score < 5
                          ? "bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/50 dark:to-red-800/50"
                          : score.score.Overall_Score <= 7
                          ? "bg-gradient-to-br from-amber-100 to-orange-200 dark:from-amber-900/50 dark:to-orange-800/50"
                          : "to-emerald-200 dark:to-emerald-800/50 bg-gradient-to-br from-green-100 dark:from-green-900/50"
                      }`}
                    >
                      <div className="text-center">
                        <Star
                          className={`mx-auto mb-1 h-6 w-6 ${
                            score.score.Overall_Score < 5
                              ? "text-red-600 dark:text-red-400"
                              : score.score.Overall_Score <= 7
                              ? "text-amber-600 dark:text-amber-400"
                              : "text-green-600 dark:text-green-400"
                          }`}
                        />
                        <div
                          className={`text-2xl font-bold ${
                            score.score.Overall_Score < 5
                              ? "text-red-700 dark:text-red-300"
                              : score.score.Overall_Score <= 7
                              ? "text-amber-700 dark:text-amber-300"
                              : "text-green-700 dark:text-green-300"
                          }`}
                        >
                          {score.score.Overall_Score || 0}
                        </div>
                      </div>
                    </div>
                    <h4 className="mb-1 font-semibold text-gray-900 dark:text-white">
                      Overall Score
                    </h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Combined Performance
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* AI Feedback & Suggestions */}
            <div className="rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900">
              <div className="border-b border-gray-200 p-4 dark:border-gray-800">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-gradient-to-br from-purple-100 to-pink-100 p-2 dark:from-purple-900/50 dark:to-pink-900/50">
                    <TrendingUp className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      AI Feedback & Suggestions
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Personalized recommendations for improvement
                    </p>
                  </div>
                </div>
              </div>
              <div className="p-6">
                {score.error ? (
                  <div className="mb-4 rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="mt-0.5 h-5 w-5 flex-shrink-0 text-red-600 dark:text-red-400" />
                      <div>
                        <h4 className="mb-1 font-medium text-red-900 dark:text-red-100">
                          Report Generation Failed
                        </h4>
                        <p className="text-sm text-red-800 dark:text-red-200">
                          {score.Suggestions}
                        </p>
                      </div>
                    </div>
                  </div>
                ) : null}

                <div className="prose prose-sm dark:prose-invert max-w-none">
                  <p className="leading-relaxed text-gray-700 dark:text-gray-300">
                    {score.Suggestions}
                  </p>
                </div>
              </div>
            </div>

            {/* Status */}
            <div className="rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900">
              <div className="border-b border-gray-200 p-4 dark:border-gray-800">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-gradient-to-br from-blue-100 to-cyan-100 p-2 dark:from-blue-900/50 dark:to-cyan-900/50">
                    <CheckCircle className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      Profile Status
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Current eligibility for job applications
                    </p>
                  </div>
                  <div className="ml-auto">
                    <div
                      className="group relative cursor-help"
                      onMouseEnter={() => showToolTip(true)}
                      onMouseLeave={() => showToolTip(false)}
                    >
                      <AlertCircle className="h-4 w-4 text-gray-400" />
                      {toolTip && (
                        <div className="absolute bottom-full right-0 z-10 mb-2 w-64 rounded-lg bg-gray-900 px-3 py-2 text-xs text-white shadow-lg">
                          Status will be Active if overall score is above 5,
                          otherwise Inactive.
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <div
                  className={`inline-flex items-center gap-2 rounded-lg px-4 py-2 font-medium ${
                    score.error
                      ? "border border-red-200 bg-red-100 text-red-800 dark:border-red-800 dark:bg-red-900/50 dark:text-red-200"
                      : score.score.Overall_Score >= 5
                      ? "border border-green-200 bg-green-100 text-green-800 dark:border-green-800 dark:bg-green-900/50 dark:text-green-200"
                      : "border border-red-200 bg-red-100 text-red-800 dark:border-red-800 dark:bg-red-900/50 dark:text-red-200"
                  }`}
                >
                  {score.error ? (
                    <AlertCircle className="h-4 w-4" />
                  ) : score.score.Overall_Score >= 5 ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <AlertCircle className="h-4 w-4" />
                  )}
                  <span>
                    {score.error
                      ? "Technical Issue"
                      : score.score.Overall_Score >= 5
                      ? "Active"
                      : "Inactive"}
                  </span>
                </div>
                {score.error ? (
                  <p className="mt-3 text-sm text-gray-600 dark:text-gray-400">
                    There was a technical issue generating your report. Please contact support.
                  </p>
                ) : score.score.Overall_Score < 5 && (
                  <p className="mt-3 text-sm text-gray-600 dark:text-gray-400">
                    Improve your score to activate your profile for job
                    applications.
                  </p>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col gap-4 sm:flex-row">
              <button
                onClick={handleDelete}
                className="inline-flex transform items-center justify-center gap-3 rounded-lg bg-red-600 px-6 py-3 font-semibold text-white shadow-lg transition-all duration-200 hover:-translate-y-0.5 hover:bg-red-700 hover:shadow-xl"
              >
                <Trash2 className="h-5 w-5" />
                Discard Video Resume
              </button>

              <button
                onClick={
                  score.error || score.score.Overall_Score < 5 ? undefined : finishVideoProfile
                }
                disabled={score.error || score.score.Overall_Score < 5}
                className={`inline-flex items-center justify-center gap-3 rounded-lg px-6 py-3 font-semibold shadow-lg transition-all duration-200 ${
                  score.error || score.score.Overall_Score < 5
                    ? "cursor-not-allowed bg-gray-400 text-gray-300 dark:bg-gray-600 dark:text-gray-400"
                    : "to-emerald-600 hover:to-emerald-700 transform bg-gradient-to-r from-green-600 text-white hover:-translate-y-0.5 hover:from-green-700 hover:shadow-xl"
                }`}
              >
                <Send className="h-5 w-5" />
                Submit Video Resume
                {score.error ? (
                  <AlertCircle className="h-4 w-4" />
                ) : score.score.Overall_Score >= 5 && (
                  <CheckCircle className="h-4 w-4" />
                )}
              </button>
          </div>

          {/* Next Focus Areas Card */}
          {score.Next_Focus_Areas && score.Next_Focus_Areas.length > 0 && !score.error && (
            <div className="mt-6 rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900">
              <div className="border-b border-gray-200 p-4 dark:border-gray-800">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-gradient-to-br from-indigo-100 to-blue-100 p-2 dark:from-indigo-900/50 dark:to-blue-900/50">
                    <TrendingUp className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      Next Focus Areas
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Areas to improve for better performance
                    </p>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <ul className="list-disc space-y-2 pl-5">
                  {score.Next_Focus_Areas.map((area, index) => (
                    <li key={index} className="text-gray-700 dark:text-gray-300">
                      {area}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}
        </div>
        </div>
      </div>
    </div>
  );
}

export default ReviewVideoProfile;
