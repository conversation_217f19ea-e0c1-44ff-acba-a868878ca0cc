import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";

const JobDescriptionCandidates = () => {
  const { state } = useLocation();
  const job = state?.job;
  const [candidates, setCandidates] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCandidates = async () => {
      setLoading(true);
      try {
        if (!job) return;
        // Fetch candidates matching role and skills
        const candRes = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/filterCandidate?role=${encodeURIComponent(job.role)}`
        );
        const candData = await candRes.json();
        let arr = [];
        if (Array.isArray(candData.candidates)) arr = candData.candidates;
        else if (Array.isArray(candData)) arr = candData;
        else arr = [];
        setCandidates(arr);
      } catch (err) {
        setCandidates([]);
      } finally {
        setLoading(false);
      }
    };
    if (job) fetchCandidates();
  }, [job]);

  if (loading) return <div>Loading...</div>;
  if (!job) return <div>Job description not found.</div>;

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">Candidates for {job.role}</h2>
      <div className="mb-2">Skills: {Array.isArray(job.skills) ? job.skills.join(", ") : job.skills}</div>
      <div className="mb-6">Location: {Array.isArray(job.location) ? job.location.join(", ") : job.location}</div>
      <div>
        {Array.isArray(candidates) && candidates.length === 0 ? (
          <div>No matching candidates found.</div>
        ) : (
          Array.isArray(candidates) &&
          candidates.map((cand) => (
            <div key={cand.id || cand._id} className="mb-4 p-3 border rounded bg-white shadow">
              <div className="font-semibold">{cand.name || cand.fullName || "No Name"}</div>
              <div>Role: {cand.role || "N/A"}</div>
              <div>Skills: {Array.isArray(cand.skills) ? cand.skills.join(", ") : cand.skills}</div>
              <div>Experience: {cand.experience || "N/A"}</div>
              <div>Location: {Array.isArray(cand.location) ? cand.location.join(", ") : cand.location}</div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default JobDescriptionCandidates;