import React, { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { useParams } from "react-router-dom";
import ProfileCard from "../ProfilesUI/ProfileCard";

const JobDescriptionProfiles = () => {
  const { id } = useParams();
  const [profiles, setProfiles] = useState([]);
  const [jd, setJD] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchJDAndProfiles() {
      setLoading(true);
      try {
        // Fetch job description details
        const jdRes = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${id}`
        );
        let jdData = null;
        if (jdRes.ok) {
          const jdJson = await jdRes.json();
          // Support both jobDescription and jobDescriptions response formats
          jdData = jdJson.jobDescription || jdJson.jobDescriptions?.find(j => j._id === Number(id));
          // If jobDescription is nested, flatten it
          if (jdData && jdData.jobDescription) {
            jdData = { ...jdData, ...jdData.jobDescription };
          }
          setJD(jdData);
          console.log("Fetched JD:", jdData);
        }
        // Fetch all candidate profiles
        // Use emp_id from cookies as fallback if jdData is missing
        const emp_id =
          jdData?.emp_id ||
          (typeof Cookies !== "undefined"
            ? Cookies.get("employerId")
            : window.localStorage.getItem("employerId") || "");
        const profilesRes = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/getSuggestedCandidates?emp_id=${emp_id}`
        );
        const profilesJson = await profilesRes.json();
        console.log("Fetched candidateProfiles:", profilesJson.candidateProfiles);
        // If no JD found, show all candidate profiles
        let matchingProfiles;
        if (!jdData) {
          console.log("JD not found, skipping filtering. Showing all candidate profiles.");
          matchingProfiles = profilesJson.candidateProfiles || [];
        } else {
          matchingProfiles = (profilesJson.candidateProfiles || []).filter(profile => {
            const roleMatches =
              profile.role?.toLowerCase() === jdData?.role?.toLowerCase();

            const profileSkills = profile.skills?.toLowerCase().split(",").map(s => s.trim());
            const jdSkills = Array.isArray(jdData?.skills)
              ? jdData.skills.map(s => s.toLowerCase())
              : (jdData?.skills || "").toLowerCase().split(",").map(s => s.trim());
            const skillsMatch = jdSkills.some(skill => profileSkills?.includes(skill));

            // Debug log for filtering
            console.log(
              "Filter check:",
              {
                profileRole: profile.role,
                jdRole: jdData?.role,
                roleMatches,
                profileSkills: profile.skills,
                jdSkills: jdData?.skills,
                skillsMatch
              }
            );

            // Relaxed: match if role OR any skill matches (ignore location/experience for now)
            return roleMatches || skillsMatch;
          });
        }
        console.log("Matching profiles:", matchingProfiles);
        setProfiles(matchingProfiles);
      } catch (err) {
        setProfiles([]);
        console.error("Error fetching JD or profiles:", err);
      } finally {
        setLoading(false);
      }
    }
    if (id) fetchJDAndProfiles();
  }, [id]);

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-4">Profiles for Job Description</h2>
      {/* Debug output removed for production */}
      {jd && (
        <div className="mb-6 p-4 rounded bg-gray-100 dark:bg-navy-700">
          <div className="font-semibold text-lg">{jd.role}</div>
          <div className="text-sm">Location: {Array.isArray(jd.location) ? jd.location.join(", ") : jd.location}</div>
          <div className="text-sm">Experience: {jd.experience}</div>
          <div className="text-sm">Skills: {Array.isArray(jd.skills) ? jd.skills.join(", ") : jd.skills}</div>
        </div>
      )}
      {loading ? (
        <div>Loading profiles...</div>
      ) : profiles.length === 0 ? (
        <div>No matching profiles found.</div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2">
          {profiles.map((profile, idx) => (
            <ProfileCard
              key={profile.id || idx}
              {...profile}
              score={profile.score}
            />
          ))}
        </div>
      )}
      {/* If no JD, show all candidate profiles */}
      {!jd && !loading && profiles.length === 0 && (
        <div className="grid gap-4 md:grid-cols-2">
          {profiles.map((profile, idx) => (
            <ProfileCard
              key={profile.id || idx}
              {...profile}
              score={profile.score}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default JobDescriptionProfiles;