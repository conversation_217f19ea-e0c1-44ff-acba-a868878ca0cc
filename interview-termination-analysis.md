# Interview Termination System Analysis

## Current Interview Flow Implementation

### 1. Question Management System

**Current Question Limits:**
- **Minimum Questions**: 5 (hardcoded in multiple places)
- **Maximum Questions**: 10 (hardcoded in backend)
- **Current Logic**: Interview can only be finished after question 5, continues until question 10 maximum

**Key Files:**
- `visume-api/controllers/questionController.js` - Backend question generation and limits
- `visume-ui/src/views/candidate/hooks/useQuestions.js` - Frontend question state management
- `visume-ui/src/views/candidate/smaller_comp/AnswerInput.jsx` - <PERSON><PERSON> controls

### 2. Current Button Control Logic

**"Next Question" Button:**
- **Location**: `AnswerInput.jsx` lines 245-265
- **Enabled When**: `!isProcessing && !isSpeaking && isInterviewActive`
- **Disabled When**: Processing, speaking, or interview inactive
- **Current Behavior**: Always enabled (no termination logic)

**"Finish Interview" Button:**
- **Location**: `AnswerInput.jsx` lines 267-315
- **Enabled When**: `currentIndex + 1 >= 5 && isInterviewActive && !isProcessing && !isSpeaking`
- **Disabled When**: Less than 5 questions completed, processing, speaking, or interview inactive
- **Current Logic**: Simple minimum question threshold (5 questions)

### 3. Requirements Tracking System

**Backend Requirements Assessment:**
- **File**: `visume-api/utils/requirementsAssessment.js`
- **Key Functions**:
  - `assessAnswerAgainstRequirements()` - Scores answers against requirements (0-10 scale)
  - `checkCompletionStatus()` - Determines if requirements are satisfied
  - `getNextRequirementToTarget()` - Identifies next requirement to focus on

**Requirements Structure:**
```javascript
{
  id: "req_technical_1",
  parameter: "JavaScript proficiency", 
  category: "technical",
  priority: "high",
  satisfied: false,
  satisfactionScore: 0,
  attemptCount: 0,
  maxAttempts: 2,
  cannotFulfill: false
}
```

**Completion Logic:**
- **Satisfaction Threshold**: Score >= 5.0 out of 10
- **Completion Threshold**: 75% of high-priority requirements satisfied
- **Max Attempts**: 2 attempts per requirement before marking as "cannot fulfill"

### 4. Current Termination Conditions

**Backend Termination (questionController.js):**
- **Hard Limit**: 10 questions maximum (lines 40-46)
- **Requirements Check**: Exists but only logs status, doesn't terminate (lines 85-90)
- **Current Behavior**: Always continues until question 10, regardless of requirement satisfaction

**Frontend Termination:**
- **Timer Expiration**: Automatically moves to next question when timer expires
- **Manual Termination**: Only via "Finish Interview" button after question 5
- **No Intelligent Logic**: No requirement-based termination

### 5. State Management

**Interview State:**
- **Hook**: `useInterviewState.js` - Manages interview lifecycle
- **States**: 'not_started', 'active', 'ended'
- **Question State**: `useQuestions.js` - Manages question progression and answers

**Key State Variables:**
- `isInterviewActive` - Controls if interview is running
- `currentIndex` - Current question index (0-based)
- `isProcessing` - Prevents actions during processing
- `isTransitioning` - Prevents actions during transitions

### 6. Audio Transcription & Video Upload

**Audio Transcription:**
- **Location**: `InterviewSection.jsx` - `transcribeAudio()` function
- **Integration**: Fully integrated with question progression
- **Storage**: Answers stored in localStorage and database

**Video Upload:**
- **Implementation**: S3 upload via pre-signed URLs
- **Location**: `DeviceTest.jsx` contains complete workflow
- **Integration**: Works with existing interview flow

## Current Gaps for Intelligent Termination

### 1. Missing Frontend Requirements Integration
- Frontend doesn't receive or track requirement satisfaction status
- No mechanism to check completion status in UI components
- Button controls don't consider requirement satisfaction

### 2. Missing Termination Logic
- No logic to disable "Next Question" when requirements satisfied
- No automatic termination after question 5 based on requirements
- No user notification system for termination reasons

### 3. Missing API Integration
- Frontend doesn't call requirement checking APIs
- No real-time requirement status updates in UI
- No communication between requirement system and button controls

### 4. Missing Maximum Attempts Tracking
- Frontend doesn't track or display attempt counts
- No logic to terminate when max attempts reached
- No user feedback about attempt limits

## Implementation Requirements

### 1. Frontend Requirements Integration
- Add requirement status tracking to question state
- Integrate requirement checking with answer submission
- Add requirement status to component props

### 2. Intelligent Button Control
- Modify button enable/disable logic based on requirements
- Add termination conditions after question 5
- Preserve existing minimum question threshold

### 3. User Notification System
- Add toast notifications for termination reasons
- Inform users when requirements satisfied
- Notify when max attempts reached

### 4. API Integration
- Call requirement checking APIs after each answer
- Update UI state based on requirement status
- Handle requirement completion responses

## Files Requiring Modification

### Primary Files:
1. `visume-ui/src/views/candidate/smaller_comp/AnswerInput.jsx` - Button control logic
2. `visume-ui/src/views/candidate/hooks/useQuestions.js` - Question state management
3. `visume-ui/src/views/candidate/components/InterviewSection/InterviewSection.jsx` - Main interview logic

### Secondary Files:
4. `visume-ui/src/views/candidate/components/InterviewSection/InterviewSectionContent.jsx` - Props passing
5. Any additional utility files for requirement status management

## Preservation Requirements

### Critical Functionality to Preserve:
- Audio transcription and recording mechanisms
- Video upload to S3 functionality  
- Score generation algorithms
- Database operations (updateAnswer, etc.)
- State management and localStorage
- Timer functionality
- All existing business logic
- Current minimum question threshold (5 questions)
