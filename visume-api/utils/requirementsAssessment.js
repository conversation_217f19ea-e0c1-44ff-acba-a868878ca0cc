const { GoogleGenerativeAI } = require('@google/generative-ai');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const dotenv = require('dotenv');

dotenv.config();

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

/**
 * Retrieve requirements for a video profile from database
 * @param {string} videoProfileId - Video profile ID
 * @returns {Object|null} Requirements object or null if not found
 */
exports.getRequirementsForProfile = async (videoProfileId) => {
  try {
    const profile = await prisma.videoprofile.findFirst({
      where: { video_profile_id: videoProfileId },
      select: { requirements: true },
    });
    if (!profile || !profile.requirements) {
      console.log(`No requirements found for video profile ${videoProfileId}`);
      return null;
    }
    try {
      const requirements = JSON.parse(profile.requirements);
      console.log(`✅ Requirements retrieved for profile ${videoProfileId}:`, {
        requirementsCount: requirements.requirements?.length || 0,
        completionThreshold: requirements.assessmentStrategy?.completionThreshold || 0.75
      });
      return requirements;
    } catch (parseError) {
      console.error("Error parsing requirements JSON:", parseError);
      return null;
    }
  } catch (err) {
    console.error("Error fetching requirements from database:", err);
    return null;
  }
};

/**
 * Assess answer against specific requirements using AI
 * @param {string} answer - Candidate's answer
 * @param {string} question - Interview question
 * @param {Array} requirements - Array of requirements to assess
 * @param {Array} previousQA - Previous questions and answers for context
 * @returns {Array} Assessment results for each requirement
 */
exports.assessAnswerAgainstRequirements = async (answer, question, requirements, previousQA = []) => {
  try {
    console.log(`🔍 REQUIREMENTS ASSESSMENT: Analyzing answer against ${requirements.length} requirements`);
    
    if (!requirements || requirements.length === 0) {
      console.log("⚠️ No requirements to assess");
      return [];
    }

    // Build context from previous Q&A
    const contextString = previousQA.length > 0 
      ? previousQA.map(qa => `Q: ${qa.question}\nA: ${qa.answer || 'No response'}`).join('\n\n')
      : 'No previous context';

    const assessmentPrompt = `You are an expert technical interviewer analyzing a candidate's answer against specific evaluation requirements.

CURRENT QUESTION: "${question}"
CURRENT ANSWER: "${answer}"

PREVIOUS INTERVIEW CONTEXT:
${contextString}

REQUIREMENTS TO ASSESS:
${requirements.map((req, index) => `
${index + 1}. REQUIREMENT ID: ${req.id}
   Parameter: ${req.parameter || req.description}
   Category: ${req.category}
   Priority: ${req.priority}
   Assessment Criteria: ${req.assessmentCriteria.join(', ')}
   Current Status: ${req.satisfied ? 'SATISFIED' : 'NOT SATISFIED'}
   Current Score: ${req.satisfactionScore}/10
`).join('')}

For each requirement, analyze whether this current answer (combined with previous context) provides evidence for satisfying the requirement.

ASSESSMENT RULES:
1. Score 0-10 based on how well the answer demonstrates the requirement
2. Consider both current answer AND previous context
3. A requirement is SATISFIED if score >= 5.0
4. Be strict but fair - look for concrete evidence
5. If answer is irrelevant to requirement, score should be low
6. If answer directly addresses requirement with good examples, score should be very high

Return EXACTLY this JSON format:
{
  "assessments": [
    {
      "requirementId": "req_id",
      "evidenceFound": true/false,
      "satisfactionScore": 7.5,
      "evidence": ["specific quote or example from answer"],
      "reasoning": "explanation of why this score was given",
      "satisfied": true/false,
      "improvementNeeded": "what candidate should demonstrate next (if not satisfied)"
    }
  ]
}`;

    const result = await model.generateContent(assessmentPrompt);
    const response = result.response.text();
    
    // Clean and parse the response
    let cleaned = response.trim().replace(/```json\n?/g, '').replace(/```\n?/g, '');
    const parsed = JSON.parse(cleaned);

    const assessments = parsed.assessments || [];
    
    // Log detailed assessment results
    console.log(`📊 ASSESSMENT RESULTS:`);
    assessments.forEach((assessment, index) => {
      const req = requirements.find(r => r.id === assessment.requirementId);
      console.log(`   ${index + 1}. ${assessment.requirementId}: ${req?.parameter || req?.description}`);
      console.log(`      Score: ${assessment.satisfactionScore}/10 | Satisfied: ${assessment.satisfied ? '✅' : '❌'}`);
      console.log(`      Evidence: ${assessment.evidenceFound ? '✅ Found' : '❌ None'}`);
      console.log(`      Reasoning: ${assessment.reasoning}`);
      if (!assessment.satisfied && assessment.improvementNeeded) {
        console.log(`      Next Focus: ${assessment.improvementNeeded}`);
      }
    });

    return assessments;

  } catch (error) {
    console.error("❌ Error in requirements assessment:", error);
    // Return empty assessments to allow interview to continue
    return [];
  }
};

/**
 * Update requirements satisfaction status based on assessment results
 * @param {Array} requirements - Original requirements array
 * @param {Array} assessments - Assessment results
 * @returns {Array} Updated requirements array
 */
exports.updateRequirementsSatisfaction = (requirements, assessments) => {
  if (!requirements || !assessments) return requirements;

  console.log(`🔄 UPDATING REQUIREMENTS: Processing ${assessments.length} assessments`);

  const updatedRequirements = requirements.map(req => {
    const assessment = assessments.find(a => a.requirementId === req.id);

    if (assessment) {
      const wasAlreadySatisfied = req.satisfied;
      const currentScore = req.satisfactionScore || 0;
      const newScore = assessment.satisfactionScore;

      // 🎯 NON-DECREASING SCORE POLICY: Only allow score increases
      const finalScore = Math.max(currentScore, newScore);
      const scoreChanged = finalScore !== currentScore;

      // Use existing attempt count (should already be incremented during question generation)
      const attemptCount = req.attemptCount || 0;
      const maxAttempts = req.maxAttempts || 2;

      // Determine if requirement cannot be fulfilled
      let cannotFulfill = req.cannotFulfill || false;
      if (attemptCount >= maxAttempts && finalScore < 5.0 && !req.satisfied) {
        cannotFulfill = true;
        console.log(`   ❌ REQUIREMENT CANNOT FULFILL: ${req.id} - Failed after ${attemptCount} attempts`);
      }

      const updatedReq = {
        ...req,
        satisfied: finalScore >= 5.0,
        satisfactionScore: finalScore,
        evidence: [...(req.evidence || []), ...(assessment.evidence || [])],
        lastAssessed: new Date().toISOString(),
        attemptCount: attemptCount,
        maxAttempts: maxAttempts,
        cannotFulfill: cannotFulfill,
        lastAttemptScore: newScore, // Track what this attempt scored
        scoreHistory: [...(req.scoreHistory || []), {
          score: newScore,
          timestamp: new Date().toISOString(),
          evidence: assessment.evidence || []
        }]
      };

      // Enhanced logging
      if (!wasAlreadySatisfied && updatedReq.satisfied) {
        console.log(`   ✅ REQUIREMENT SATISFIED: ${req.id} - Score: ${finalScore}/10 (${attemptCount} attempts made)`);
      } else if (cannotFulfill && !req.cannotFulfill) {
        console.log(`   ❌ REQUIREMENT MARKED CANNOT FULFILL: ${req.id} - Max attempts reached (${attemptCount}/${maxAttempts})`);
      } else if (scoreChanged) {
        console.log(`   📈 REQUIREMENT SCORE IMPROVED: ${req.id} - ${currentScore} → ${finalScore}/10 (${attemptCount} attempts made)`);
      } else {
        console.log(`   📊 REQUIREMENT ASSESSED: ${req.id} - Score maintained at ${finalScore}/10 (${attemptCount} attempts made)`);
      }

      return updatedReq;
    }

    return req;
  });

  return updatedRequirements;
};

/**
 * Use AI to select a random termination point between questions 6-9 for early satisfaction cases
 * @param {Object} requirements - Requirements object with candidate context
 * @param {Array} candidateResponses - Array of candidate responses for context
 * @param {string} jobRole - Target job role
 * @param {Array} skills - Required skills array
 * @returns {Promise<Object>} AI-selected termination information
 */
exports.selectDynamicTerminationPoint = async (requirements, candidateResponses, jobRole, skills) => {
  try {
    const candidateContext = requirements.candidateContext || {};
    const satisfiedRequirements = requirements.requirements.filter(req => req.satisfied);

    const prompt = `You are an expert interview coordinator making a strategic decision about interview length.

CONTEXT:
- Role: ${jobRole}
- Skills: ${skills.join(', ')}
- Experience Level: ${candidateContext.experienceLevel || 'Unknown'}
- Current Question: ${candidateResponses.length}
- Satisfied Requirements: ${satisfiedRequirements.length}/${requirements.requirements.length}

CANDIDATE PERFORMANCE SUMMARY:
${satisfiedRequirements.map(req => `✅ ${req.parameter} (Score: ${req.satisfactionScore}/10)`).join('\n')}

SITUATION: The candidate has satisfied ALL requirements before question 5, demonstrating strong competency early in the interview.

TASK: Select an optimal interview termination point between questions 6-9 (inclusive) based on:
1. Candidate's demonstrated competency level
2. Role complexity and seniority
3. Providing adequate assessment time without over-testing
4. Balancing efficiency with thoroughness

SELECTION CRITERIA:
- Question 6: For candidates showing exceptional early performance
- Question 7: For solid performers who met requirements efficiently
- Question 8: For good performers who need slightly more validation
- Question 9: For adequate performers who barely met early requirements

Return EXACTLY this JSON structure:
{
  "selectedTerminationPoint": 7,
  "reasoning": "Candidate demonstrated solid technical and communication skills early. Question 7 provides adequate additional assessment without over-testing.",
  "confidenceLevel": "high",
  "assessmentSummary": "Strong early performance warrants efficient interview conclusion"
}`;

    const result = await model.generateContent(prompt);
    const response = result.response.text();

    // Clean and parse the response
    let cleaned = response.trim().replace(/```json\n?/g, '').replace(/```\n?/g, '');
    const parsed = JSON.parse(cleaned);

    // Validate and ensure termination point is within valid range
    let terminationPoint = parseInt(parsed.selectedTerminationPoint);
    if (isNaN(terminationPoint) || terminationPoint < 6 || terminationPoint > 9) {
      // Fallback to random selection within valid range
      terminationPoint = Math.floor(Math.random() * 4) + 6; // Random between 6-9
      console.log(`⚠️ AI selection invalid, using fallback: ${terminationPoint}`);
    }

    return {
      selectedTerminationPoint: terminationPoint,
      reasoning: parsed.reasoning || 'AI-selected termination point for early satisfaction',
      confidenceLevel: parsed.confidenceLevel || 'medium',
      assessmentSummary: parsed.assessmentSummary || 'Dynamic termination based on early requirements satisfaction',
      selectionMethod: 'AI-based',
      generatedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('Error in AI termination selection:', error);
    // Fallback to random selection
    const fallbackPoint = Math.floor(Math.random() * 4) + 6; // Random between 6-9
    return {
      selectedTerminationPoint: fallbackPoint,
      reasoning: 'Fallback random selection due to AI error',
      confidenceLevel: 'low',
      assessmentSummary: 'Random termination point selected as fallback',
      selectionMethod: 'fallback-random',
      generatedAt: new Date().toISOString(),
      error: error.message
    };
  }
};

/**
 * Check if requirements are satisfied early (before question 5) and trigger dynamic termination
 * @param {Object} requirements - Requirements object with strategy
 * @param {number} currentQuestionCount - Current question number
 * @returns {Object} Early completion status and recommendation
 */
exports.checkEarlyCompletionStatus = (requirements, currentQuestionCount) => {
  const MIN_QUESTIONS_BEFORE_DYNAMIC_TERMINATION = 5;

  if (!requirements || !requirements.requirements || currentQuestionCount >= MIN_QUESTIONS_BEFORE_DYNAMIC_TERMINATION) {
    return {
      needsDynamicTermination: false,
      reason: currentQuestionCount >= MIN_QUESTIONS_BEFORE_DYNAMIC_TERMINATION ?
        'Past minimum question threshold' : 'No requirements available',
      currentQuestionCount,
      minQuestionsThreshold: MIN_QUESTIONS_BEFORE_DYNAMIC_TERMINATION
    };
  }

  const reqs = requirements.requirements;
  const strategy = requirements.assessmentStrategy || {};

  // Count high priority requirements
  const highPriorityReqs = reqs.filter(r => r.priority === 'high');
  const satisfiedHighPriority = highPriorityReqs.filter(r => r.satisfied);
  const cannotFulfillHighPriority = highPriorityReqs.filter(r => r.cannotFulfill === true);

  // Calculate completion metrics
  const totalRequired = Math.max(highPriorityReqs.length, strategy.minRequiredSatisfied || 3);
  const satisfiedCount = satisfiedHighPriority.length;
  const cannotFulfillCount = cannotFulfillHighPriority.length;
  const effectiveSatisfiedCount = satisfiedCount + cannotFulfillCount;

  const completionPercentage = totalRequired > 0 ? (satisfiedCount / totalRequired) : 0;
  const effectiveCompletionPercentage = totalRequired > 0 ? (effectiveSatisfiedCount / totalRequired) : 0;
  const threshold = strategy.completionThreshold || 0.75;

  // Check if ALL requirements are either satisfied or have reached max attempts (exhausted)
  const allRequirementsComplete = reqs.every(r => {
    const maxAttempts = r.maxAttempts || 2;
    return r.satisfied || r.cannotFulfill || (r.attemptCount || 0) >= maxAttempts;
  });

  // Determine if dynamic termination is needed (ALL requirements satisfied)
  const allRequirementsSatisfied = reqs.every(r => r.satisfied);
  const needsDynamicTermination = allRequirementsSatisfied && currentQuestionCount < MIN_QUESTIONS_BEFORE_DYNAMIC_TERMINATION;

  let reason = 'Requirements not yet satisfied';
  let terminationType = 'none';

  if (needsDynamicTermination) {
    reason = 'All requirements satisfied before minimum questions - dynamic termination triggered';
    terminationType = 'dynamic';
  } else if (allRequirementsComplete && !allRequirementsSatisfied) {
    reason = 'Mixed status: some satisfied, others exhausted - continue with extended attempts';
    terminationType = 'extended';
  }

  return {
    needsDynamicTermination,
    terminationType,
    reason,
    currentQuestionCount,
    minQuestionsThreshold: MIN_QUESTIONS_BEFORE_DYNAMIC_TERMINATION,
    completionMetrics: {
      satisfiedCount,
      totalRequired,
      cannotFulfillCount,
      completionPercentage: Math.round(completionPercentage * 100),
      effectiveCompletionPercentage: Math.round(effectiveCompletionPercentage * 100),
      threshold: Math.round(threshold * 100),
      allRequirementsComplete,
      allRequirementsSatisfied
    },
    satisfiedRequirements: satisfiedHighPriority.map(r => ({
      id: r.id,
      parameter: r.parameter || r.description,
      score: r.satisfactionScore
    })),
    exhaustedRequirements: reqs.filter(r => r.cannotFulfill || (r.attemptCount || 0) >= (r.maxAttempts || 2)).map(r => ({
      id: r.id,
      parameter: r.parameter || r.description,
      reason: r.cannotFulfill ? 'Cannot fulfill' : 'Max attempts reached'
    }))
  };
};

/**
 * Check if interview can be automatically completed based on requirements
 * @param {Object} requirements - Requirements object with strategy
 * @param {Object} options - Additional options for completion check
 * @returns {Object} Completion status information
 */
exports.checkCompletionStatus = (requirements, options = {}) => {
  if (!requirements || !requirements.requirements) {
    return {
      canAutoComplete: false,
      satisfiedCount: 0,
      totalRequired: 0,
      completionPercentage: 0,
      nextFocusArea: null,
      cannotFulfillCount: 0,
      effectiveCompletionPercentage: 0,
      minAttemptsReached: false
    };
  }

  const reqs = requirements.requirements;
  const strategy = requirements.assessmentStrategy || {};
  const minAttemptsPerRequirement = options.minAttemptsPerRequirement || 1;

  // Count high priority requirements
  const highPriorityReqs = reqs.filter(r => r.priority === 'high');
  const satisfiedHighPriority = highPriorityReqs.filter(r => r.satisfied);
  const cannotFulfillHighPriority = highPriorityReqs.filter(r => r.cannotFulfill === true);

  // Calculate completion metrics
  const totalRequired = Math.max(highPriorityReqs.length, strategy.minRequiredSatisfied || 3);
  const satisfiedCount = satisfiedHighPriority.length;
  const cannotFulfillCount = cannotFulfillHighPriority.length;

  // Standard completion percentage (satisfied / total)
  const completionPercentage = totalRequired > 0 ? satisfiedCount / totalRequired : 0;

  // Enhanced completion percentage (satisfied + cannot fulfill) / total
  const effectiveCompletionPercentage = totalRequired > 0 ?
    (satisfiedCount + cannotFulfillCount) / totalRequired : 0;

  // Check if minimum attempts have been made for all requirements
  const allReqsHaveMinAttempts = reqs.every(r => (r.attemptCount || 0) >= minAttemptsPerRequirement);

  // Check if can auto-complete based on:
  // 1. Standard threshold of satisfied requirements OR
  // 2. All remaining unsatisfied requirements are marked as "cannot fulfill" AND minimum attempts made OR
  // 3. ALL requirements have either been satisfied or reached their 2-attempt limit
  const threshold = strategy.completionThreshold || 0.75;

  // Check if ALL requirements are either satisfied or have reached max attempts (2)
  const allRequirementsComplete = reqs.every(r => {
    const maxAttempts = r.maxAttempts || 2;
    return r.satisfied || r.cannotFulfill || (r.attemptCount || 0) >= maxAttempts;
  });

  const canAutoComplete =
    completionPercentage >= threshold ||
    (effectiveCompletionPercentage >= threshold && allReqsHaveMinAttempts) ||
    allRequirementsComplete;

  // Identify next focus area - prioritize requirements that are not "cannot fulfill"
  const viableUnsatisfiedHighPriority = highPriorityReqs.filter(r => !r.satisfied && !r.cannotFulfill);
  const nextFocusArea = viableUnsatisfiedHighPriority.length > 0 ?
    viableUnsatisfiedHighPriority[0].category : null;

  // Get next requirement to focus on
  const nextRequirementFocus = viableUnsatisfiedHighPriority.length > 0 ?
    viableUnsatisfiedHighPriority.sort((a, b) => {
      // Prioritize by: 1) lowest score 2) fewest attempts
      if (a.satisfactionScore !== b.satisfactionScore) {
        return a.satisfactionScore - b.satisfactionScore; // Lower score first
      }
      return (a.attemptCount || 0) - (b.attemptCount || 0); // Fewer attempts first
    })[0] : null;

  const status = {
    canAutoComplete,
    satisfiedCount,
    totalRequired,
    completionPercentage: Math.round(completionPercentage * 100),
    effectiveCompletionPercentage: Math.round(effectiveCompletionPercentage * 100),
    cannotFulfillCount,
    minAttemptsReached: allReqsHaveMinAttempts,
    nextFocusArea,
    nextRequirementFocus: nextRequirementFocus ? {
      id: nextRequirementFocus.id,
      parameter: nextRequirementFocus.parameter || nextRequirementFocus.description,
      category: nextRequirementFocus.category,
      currentScore: nextRequirementFocus.satisfactionScore,
      attemptCount: nextRequirementFocus.attemptCount || 0
    } : null,
    unsatisfiedRequirements: viableUnsatisfiedHighPriority.map(r => ({
      id: r.id,
      parameter: r.parameter || r.description,
      category: r.category,
      currentScore: r.satisfactionScore,
      attemptCount: r.attemptCount || 0
    })),
    cannotFulfillRequirements: cannotFulfillHighPriority.map(r => ({
      id: r.id,
      parameter: r.parameter || r.description,
      category: r.category,
      currentScore: r.satisfactionScore,
      attemptCount: r.attemptCount || 0
    }))
  };

  console.log(`📈 COMPLETION STATUS:`, {
    satisfied: `${satisfiedCount}/${totalRequired} high-priority requirements satisfied`,
    cannotFulfill: `${cannotFulfillCount}/${totalRequired} high-priority requirements cannot fulfill`,
    standardPercentage: `${status.completionPercentage}%`,
    effectivePercentage: `${status.effectiveCompletionPercentage}%`,
    minAttemptsReached: allReqsHaveMinAttempts ? '✅ Yes' : '❌ No',
    allRequirementsComplete: allRequirementsComplete ? '✅ All done (2-attempt limit)' : '❌ Some requirements pending',
    canComplete: canAutoComplete ? '✅ Ready for completion' : '❌ More assessment needed',
    nextFocus: nextFocusArea || 'No viable requirements to focus on'
  });

  return status;
};

/**
 * Get requirements that need extended attempts (mixed status scenario)
 * @param {Object} requirements - Requirements object
 * @param {number} currentQuestionCount - Current question number
 * @returns {Array} Requirements that can benefit from extended attempts
 */
exports.getRequirementsForExtendedAttempts = (requirements, currentQuestionCount) => {
  if (!requirements || !requirements.requirements) {
    return [];
  }

  const reqs = requirements.requirements;

  // In mixed status scenarios, identify requirements that:
  // 1. Are not satisfied
  // 2. Have not been marked as "cannot fulfill"
  // 3. Have some evidence of partial success (score > 0)
  // 4. Could benefit from different question approaches

  const extendedAttemptCandidates = reqs.filter(r => {
    const hasPartialSuccess = (r.satisfactionScore || 0) > 0 && (r.satisfactionScore || 0) < 5.0;
    const hasEvidence = r.evidence && r.evidence.length > 0;
    const notExhausted = !r.cannotFulfill && (r.attemptCount || 0) < (r.maxAttempts || 2);
    const notSatisfied = !r.satisfied;

    return notSatisfied && (hasPartialSuccess || hasEvidence) && notExhausted;
  });

  // Sort by potential for improvement (higher partial scores first)
  extendedAttemptCandidates.sort((a, b) => {
    const aScore = a.satisfactionScore || 0;
    const bScore = b.satisfactionScore || 0;
    return bScore - aScore; // Higher score first (more likely to succeed)
  });

  console.log(`🔄 EXTENDED ATTEMPTS ANALYSIS (Question ${currentQuestionCount}):`);
  console.log(`   - Candidates for extended attempts: ${extendedAttemptCandidates.length}`);
  extendedAttemptCandidates.forEach((req, index) => {
    console.log(`   ${index + 1}. ${req.parameter}: Score ${req.satisfactionScore || 0}/10, Attempts: ${req.attemptCount || 0}/${req.maxAttempts || 2}`);
  });

  return extendedAttemptCandidates;
};

/**
 * Reset attempt counts for requirements in mixed status scenarios to give more chances
 * @param {Object} requirements - Requirements object
 * @param {Array} targetRequirements - Specific requirements to reset (optional)
 * @returns {Object} Updated requirements object
 */
exports.resetAttemptsForMixedStatus = (requirements, targetRequirements = null) => {
  if (!requirements || !requirements.requirements) {
    return requirements;
  }

  const requirementsToReset = targetRequirements ||
    exports.getRequirementsForExtendedAttempts(requirements, 0);

  const updatedRequirements = {
    ...requirements,
    requirements: requirements.requirements.map(req => {
      const shouldReset = requirementsToReset.some(target => target.id === req.id);

      if (shouldReset) {
        console.log(`🔄 RESETTING ATTEMPTS: ${req.id} - ${req.parameter}`);
        console.log(`   - Previous attempts: ${req.attemptCount || 0}/${req.maxAttempts || 2}`);
        console.log(`   - Previous score: ${req.satisfactionScore || 0}/10`);

        return {
          ...req,
          attemptCount: 0, // Reset attempt count
          maxAttempts: 3, // Give one extra attempt in mixed scenarios
          cannotFulfill: false, // Clear cannot fulfill flag
          lastReset: new Date().toISOString(),
          extendedAttempts: true // Mark as having extended attempts
        };
      }

      return req;
    }),
    lastMixedStatusReset: new Date().toISOString()
  };

  console.log(`✅ MIXED STATUS RESET COMPLETED: ${requirementsToReset.length} requirements reset`);
  return updatedRequirements;
};

/**
 * Get the next requirement to target for question generation
 * @param {Object} requirements - Requirements object
 * @returns {Object|null} Next requirement to focus on or null if none
 */
exports.getNextRequirementToTarget = (requirements) => {
  if (!requirements || !requirements.requirements) {
    return null;
  }

  const reqs = requirements.requirements;

  // Filter out satisfied and cannot fulfill requirements
  // Also filter out requirements that have reached max attempts without being satisfied
  const viableRequirements = reqs.filter(r => {
    const maxAttempts = r.maxAttempts || 2;
    const attemptCount = r.attemptCount || 0;
    const hasReachedMaxAttempts = attemptCount >= maxAttempts;

    return !r.satisfied && !r.cannotFulfill && !hasReachedMaxAttempts;
  });

  // If no viable requirements, check if there are any requirements that haven't reached max attempts yet
  if (viableRequirements.length === 0) {
    const requirementsWithAttemptsLeft = reqs.filter(r => {
      const maxAttempts = r.maxAttempts || 2;
      const attemptCount = r.attemptCount || 0;
      return !r.satisfied && !r.cannotFulfill && attemptCount < maxAttempts;
    });

    if (requirementsWithAttemptsLeft.length === 0) {
      console.log(`🎯 NO VIABLE REQUIREMENTS: All requirements are either satisfied, cannot fulfill, or have reached max attempts`);
      return null;
    } else {
      console.log(`🎯 USING REQUIREMENTS WITH ATTEMPTS LEFT: ${requirementsWithAttemptsLeft.length} requirements still have attempts remaining`);
      // Use requirements that still have attempts left
      viableRequirements.push(...requirementsWithAttemptsLeft);
    }
  }

  // Sort by priority (high first), then by fewest attempts, then by lowest score
  const sortedRequirements = viableRequirements.sort((a, b) => {
    // Priority: high > medium > low
    const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
    const aPriority = priorityOrder[a.priority] || 1;
    const bPriority = priorityOrder[b.priority] || 1;

    if (aPriority !== bPriority) {
      return bPriority - aPriority; // Higher priority first
    }

    // Then by fewest attempts (ensure fair rotation)
    const aAttempts = a.attemptCount || 0;
    const bAttempts = b.attemptCount || 0;
    if (aAttempts !== bAttempts) {
      return aAttempts - bAttempts; // Fewer attempts first
    }

    // Finally by lowest score (needs most improvement)
    const aScore = a.satisfactionScore || 0;
    const bScore = b.satisfactionScore || 0;
    return aScore - bScore; // Lower score first
  });

  const nextRequirement = sortedRequirements[0];

  console.log(`🎯 NEXT REQUIREMENT TARGET:`, {
    id: nextRequirement.id,
    parameter: nextRequirement.parameter || nextRequirement.description?.substring(0, 80) + '...',
    category: nextRequirement.category,
    priority: nextRequirement.priority,
    currentScore: nextRequirement.satisfactionScore || 0,
    attemptCount: nextRequirement.attemptCount || 0,
    maxAttempts: nextRequirement.maxAttempts || 2,
    attemptsRemaining: (nextRequirement.maxAttempts || 2) - (nextRequirement.attemptCount || 0)
  });

  // Log summary of all viable requirements for debugging
  console.log(`📊 VIABLE REQUIREMENTS SUMMARY:`);
  sortedRequirements.slice(0, 3).forEach((req, index) => {
    console.log(`   ${index + 1}. ${req.id}: ${req.attemptCount || 0}/${req.maxAttempts || 2} attempts, score: ${req.satisfactionScore || 0}/10`);
  });

  return nextRequirement;
};

/**
 * Increment attempt count for a specific requirement when a question targets it
 * @param {Object} requirements - Requirements object
 * @param {string} targetRequirementId - ID of the requirement being targeted
 * @returns {Object} Updated requirements object
 */
exports.incrementRequirementAttempt = (requirements, targetRequirementId) => {
  if (!requirements || !requirements.requirements || !targetRequirementId) {
    return requirements;
  }

  console.log(`🎯 INCREMENTING ATTEMPT COUNT for requirement: ${targetRequirementId}`);

  const updatedRequirements = {
    ...requirements,
    requirements: requirements.requirements.map(req => {
      if (req.id === targetRequirementId) {
        const newAttemptCount = (req.attemptCount || 0) + 1;
        const maxAttempts = req.maxAttempts || 2;

        console.log(`   📊 ATTEMPT COUNT: ${req.id} - ${req.attemptCount || 0} → ${newAttemptCount} of ${maxAttempts}`);

        return {
          ...req,
          attemptCount: newAttemptCount,
          maxAttempts: maxAttempts,
          lastTargeted: new Date().toISOString()
        };
      }
      return req;
    })
  };

  return updatedRequirements;
};

/**
 * Update requirements in database
 * @param {string} videoProfileId - Video profile ID
 * @param {Object} requirements - Updated requirements object
 * @returns {Promise<boolean>} Success status
 */
exports.updateRequirementsInDatabase = async (videoProfileId, requirements) => {
  try {
    const requirementsJson = JSON.stringify(requirements);
    await prisma.videoprofile.updateMany({
      where: { video_profile_id: videoProfileId },
      data: { requirements: requirementsJson },
    });
    console.log(`💾 Requirements updated in database for profile ${videoProfileId}`);
    return true;
  } catch (error) {
    console.error("Error updating requirements in database:", error);
    return false;
  }
};
